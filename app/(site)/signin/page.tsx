import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card"
import type { Metadata } from "next"
import { SupabaseAuth } from "@/components/auth/supabase-auth"

export const metadata: Metadata = {
  title: "Sign In | Introducing.day",
  description: "Sign in to your Introducing.day account",
}

export default function SignInPage() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Welcome back</CardTitle>
          <CardDescription>Sign in to your Introducing.day account</CardDescription>
        </CardHeader>
        <CardContent>
          <SupabaseAuth view="sign_in" />
          <div className="mt-6 text-center text-sm text-gray-600">
            Don&apos;t have an account?{" "}
            <Link href="/signup" className="font-medium text-black hover:underline">
              Sign up
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
