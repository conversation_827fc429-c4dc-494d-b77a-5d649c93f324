import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card"
import type { Metadata } from "next"
import { SupabaseAuth } from "@/components/auth/supabase-auth"

export const metadata: Metadata = {
  title: "Sign Up | Introducing.day",
  description: "Join the Introducing.day community",
}

export default function SignUpPage() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Join Introducing.Day</CardTitle>
          <CardDescription>Create your account to discover and share amazing products</CardDescription>
        </CardHeader>
        <CardContent>
          <SupabaseAuth view="sign_up" />
          <div className="mt-6 text-center text-sm text-gray-600">
            Already have an account?{" "}
            <Link href="/signin" className="font-medium text-black hover:underline">
              Sign in
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
